using Orleans;
using Orleans.EventSourcing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Services;
using Curio.Infrastructure.Configuration;
using Curio.Shared.Users;
using Microsoft.Extensions.Configuration;
using Curio.Infrastructure; // for GetOrleansConnectionString extension

namespace Curio.Orleans.Grains.Base;

/// <summary>
/// 提供弹性事件发布能力的基础JournaledGrain
/// 所有业务Grain都应该继承此类而不是直接继承JournaledGrain
/// </summary>
public abstract class ResilientJournaledGrain<TState, TEvent> : JournaledGrain<TState, TEvent>
    where TState : class, new()
    where TEvent : DomainEvent
{
    private IResilientEventPublisher? _eventPublisher;
    protected ILogger Logger { get; private set; } = null!;
    // 缓存本次命令期间已 Raise 但尚未发布的事件
    private readonly List<TEvent> _pendingEventsForPublish = new();
    // 已确认的事件累计计数（用于简单快照策略）
    private long _confirmedEventCount = 0;

    // 简单快照配置（可后续改为配置化）
    protected virtual int SnapshotInterval => 50; // 每确认50个事件生成一次快照示例
    private static bool _snapshotTableEnsured = false;

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);

        // 获取日志记录器
        Logger = ServiceProvider.GetRequiredService<ILogger<ResilientJournaledGrain<TState, TEvent>>>();

        // 获取StreamProvider（通过Grain的GetStreamProvider方法）
        var streamProvider = this.GetStreamProvider("KafkaStreams");

        // 创建弹性事件发布器
        var options = ServiceProvider.GetRequiredService<IOptions<ResilientPublishOptions>>();
        var publisherLogger = ServiceProvider.GetRequiredService<ILogger<ResilientEventPublisher>>();
        _eventPublisher = new ResilientEventPublisher(streamProvider, publisherLogger, options);

        Logger.LogDebug("ResilientJournaledGrain activated: {GrainType}, {GrainId}",
            GetType().Name, this.GetPrimaryKeyString());

        // 尝试加载最近快照以加速后续统计与发布策略（注意：当前 Orleans 仍已完成事件重放；此处主要用于减少我们自定义计数的冷启动成本）
        await TryRestoreSnapshotMetadataAsync();
    }

    /// <summary>
    /// JournaledGrain自定义事件处理逻辑
    /// </summary>
    protected override void OnStateChanged()
    {
        base.OnStateChanged();

        // 在状态变更后发布最新的事件
        // 注意：这里需要根据实际的JournaledGrain API调整
    }

    /// <summary>
    /// 封装 RaiseEvent 以便记录需要对外发布的事件
    /// （隐藏基类同名方法，不改其语义）
    /// </summary>
    protected void RaiseEvent(TEvent @event)
    {
        base.RaiseEvent(@event);
        _pendingEventsForPublish.Add(@event);
    }

    /// <summary>
    /// 一次性确认所有暂存事件并尝试发布，再按策略生成快照
    /// </summary>
    protected async Task ConfirmAndPublishAsync()
    {
        var toPublish = _pendingEventsForPublish.ToList();
        await ConfirmEvents(); // 持久化 + 回放
        _confirmedEventCount += toPublish.Count;

        if (toPublish.Count > 0 && _eventPublisher != null)
        {
            try
            {
                await PublishEventsAsync(toPublish);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed publishing events batch after confirm. Events will remain persisted but not externally published.");
            }
        }

        _pendingEventsForPublish.Clear();

        // 快照策略（示例：文件系统）；真实生产可改为集中存储或 Orleans Snapshot Provider
        if (SnapshotInterval > 0 && _confirmedEventCount % SnapshotInterval == 0)
        {
            await TryCreateSnapshotAsync();
        }
    }

    private async Task TryCreateSnapshotAsync()
    {
        try
        {
            var grainId = this.GetPrimaryKeyString();
            var sp = ServiceProvider;
            var configuration = sp.GetService(typeof(IConfiguration)) as IConfiguration;
            if (configuration == null)
            {
                Logger.LogWarning("Snapshot skipped: IConfiguration not available");
                return;
            }
            // 使用存储连接串（与 Orleans Storage 共用）
            var connStr = configuration.GetOrleansConnectionString("storage");
            await EnsureSnapshotTableAsync(connStr);

            var snapshotEnvelope = new SnapshotEnvelope<TState>
            {
                GrainId = grainId,
                Version = _confirmedEventCount,
                TakenAtUtc = DateTime.UtcNow,
                State = this.State
            };
            var json = System.Text.Json.JsonSerializer.Serialize(snapshotEnvelope, new System.Text.Json.JsonSerializerOptions { WriteIndented = false });

            await using var conn = new Npgsql.NpgsqlConnection(connStr);
            await conn.OpenAsync();
            await using var cmd = new Npgsql.NpgsqlCommand(@"INSERT INTO grain_snapshots(grain_id, grain_type, version, taken_at_utc, state_json)
VALUES (@g,@t,@v,@ts::timestamptz, to_jsonb(@payload::json))
ON CONFLICT (grain_id, version) DO NOTHING;", conn);
            cmd.Parameters.AddWithValue("g", grainId);
            cmd.Parameters.AddWithValue("t", GetType().Name);
            cmd.Parameters.AddWithValue("v", _confirmedEventCount);
            cmd.Parameters.AddWithValue("ts", snapshotEnvelope.TakenAtUtc);
            cmd.Parameters.AddWithValue("payload", json);
            await cmd.ExecuteNonQueryAsync();
            Logger.LogDebug("Snapshot persisted: Grain={Grain} Version={Version}", grainId, _confirmedEventCount);
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Create snapshot failed (non-fatal).");
        }
    }

    private static readonly object _lock = new();
    private static async Task EnsureSnapshotTableAsync(string connStr)
    {
        if (_snapshotTableEnsured) return;
        lock (_lock)
        {
            if (_snapshotTableEnsured) return;
            _snapshotTableEnsured = true; // 先标记，失败不再阻塞主流程
        }
        try
        {
            await using var conn = new Npgsql.NpgsqlConnection(connStr);
            await conn.OpenAsync();
            var sql = @"CREATE TABLE IF NOT EXISTS grain_snapshots (
    grain_id TEXT NOT NULL,
    grain_type TEXT NOT NULL,
    version BIGINT NOT NULL,
    taken_at_utc TIMESTAMPTZ NOT NULL,
    state_json JSONB NOT NULL,
    PRIMARY KEY (grain_id, version)
);
CREATE INDEX IF NOT EXISTS idx_grain_snapshots_type_version ON grain_snapshots(grain_type, version);";
            await using var cmd = new Npgsql.NpgsqlCommand(sql, conn);
            await cmd.ExecuteNonQueryAsync();
        }
        catch
        {
            // 静默：失败不阻断业务，后续 Snapshot 会尝试插入而失败被捕获
        }
    }

    private async Task TryRestoreSnapshotMetadataAsync()
    {
        try
        {
            var sp = ServiceProvider;
            var configuration = sp.GetService(typeof(IConfiguration)) as IConfiguration;
            if (configuration == null) return;
            var connStr = configuration.GetOrleansConnectionString("storage");
            await using var conn = new Npgsql.NpgsqlConnection(connStr);
            await conn.OpenAsync();
            await using var cmd = new Npgsql.NpgsqlCommand(@"SELECT version, state_json FROM grain_snapshots WHERE grain_id=@g ORDER BY version DESC LIMIT 1;", conn);
            cmd.Parameters.AddWithValue("g", this.GetPrimaryKeyString());
            await using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var version = reader.GetInt64(0);
                var json = reader.GetString(1);
                // 如果我们内部计数还是0，则用快照版本初始化计数
                if (_confirmedEventCount == 0 && version > 0)
                {
                    _confirmedEventCount = version;
                    // 如果当前 State 似乎是“空”状态（尝试通过序列化判断）则直接反序列化覆盖
                    if (State != null && IsDefaultState(State))
                    {
                        var env = System.Text.Json.JsonSerializer.Deserialize<SnapshotEnvelope<TState>>(json);
                        if (env?.State != null)
                        {
                            // 覆盖当前状态（注意：这不会回滚 Orleans 已经应用的事件；在极端情况下可能与内部事件回放重复，但只要事件是确定性可重放就安全）
                            CopyState(env.State, State);
                        }
                    }
                    Logger.LogDebug("Snapshot metadata restored: Grain={Grain} Version={Version}", this.GetPrimaryKeyString(), version);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogDebug(ex, "Snapshot restore skipped (non-fatal)");
        }
    }

    // 简单判断 State 是否“空”——根据所有公共可读属性是否是默认值（浅判断）
    private static bool IsDefaultState(TState state)
    {
        try
        {
            foreach (var prop in typeof(TState).GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance))
            {
                if (!prop.CanRead) continue;
                var val = prop.GetValue(state);
                if (val == null) continue;
                var type = prop.PropertyType;
                object? defaultVal = type.IsValueType ? Activator.CreateInstance(type) : null;
                if (!Equals(val, defaultVal)) return false;
            }
        }
        catch { return false; }
        return true;
    }

    // 通过反射浅复制属性（仅可写）
    private static void CopyState(TState source, TState target)
    {
        foreach (var prop in typeof(TState).GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance))
        {
            if (!prop.CanRead || !prop.CanWrite) continue;
            try
            {
                var value = prop.GetValue(source);
                prop.SetValue(target, value);
            }
            catch { /* ignore */ }
        }
    }

    [GenerateSerializer]
    protected class SnapshotEnvelope<T>
    {
        [Id(0)] public string GrainId { get; set; } = string.Empty;
        [Id(1)] public long Version { get; set; }
        [Id(2)] public DateTime TakenAtUtc { get; set; }
        [Id(3)] public T State { get; set; } = default!;
    }

    /// <summary>
    /// 发布事件的安全包装方法
    /// </summary>
    protected async Task PublishEventSafelyAsync(TEvent @event)
    {
        if (_eventPublisher == null)
        {
            Logger.LogError("EventPublisher is null, cannot publish event: {EventId}", @event.EventId);
            return;
        }

        try
        {
            await _eventPublisher.PublishAsync(@event, this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish event through resilient publisher: {EventId}", @event.EventId);
            // 不重新抛出异常，避免影响业务流程
            // 弹性发布器内部已经处理了重试和失败逻辑
        }
    }

    /// <summary>
    /// 提供批量发布能力
    /// </summary>
    protected async Task PublishEventsAsync(IEnumerable<TEvent> events)
    {
        if (_eventPublisher == null)
        {
            Logger.LogError("EventPublisher is null, cannot publish events");
            return;
        }

        try
        {
            await _eventPublisher.PublishBatchAsync(events, this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish events batch through resilient publisher");
        }
    }

    /// <summary>
    /// 安全的事件发布方法，包含异常处理
    /// </summary>
    protected async Task<bool> TryPublishEventAsync(TEvent @event)
    {
        try
        {
            await PublishEventSafelyAsync(@event);
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish event: {EventId}", @event.EventId);
            return false;
        }
    }
}