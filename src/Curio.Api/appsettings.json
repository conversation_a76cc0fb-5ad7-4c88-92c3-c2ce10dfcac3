{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Curio.Infrastructure.Services": "Information", "Orleans": "Information", "Microsoft.Orleans": "Warning"}}, "AllowedHosts": "*", "DefaultAdmin": {"Username": "<EMAIL>", "Email": "<EMAIL>", "Name": "System Administrator", "Password": "Admin123!"}, "Application": {"Name": "Curio API", "Version": "1.0.0", "Api": {"BaseUrl": "https://localhost:7274", "AllowedHosts": ["*"], "RequestTimeoutSeconds": 30, "MaxRequestBodySize": 10485760, "Cors": {"Enabled": true, "AllowedOrigins": ["*"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}}, "Security": {"Jwt": {"Issuer": "<PERSON><PERSON><PERSON>", "Audience": "Curio.Api", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}}}, "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=curio-api;Username=********;Password=********", "Host": "localhost", "Port": 5432, "Database": "curio-api", "Username": "********", "Password": "********", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "curio-cluster", "ServiceId": "curio-service", "Clustering": {"Provider": "AdoNet", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka"}, "Reminders": {"Provider": "AdoNet"}}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "PLAINTEXT"}, "Email": {"Smtp": {"Host": "smtp.gmail.com", "Port": 587, "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromName": "<PERSON><PERSON><PERSON>", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}